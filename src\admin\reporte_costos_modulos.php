<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en reporte_costos_modulos.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$reporte_costos_modulos = [];
$filtro_proyecto_id     = null;
$nombre_proyecto_filtro = '';
$success_text           = '';
$success_display        = 'none';
$error_text             = '';
$error_display          = 'none';
$total_costo_usd        = 0.0;
$total_n_mensajes       = 0;
$total_costo_mensajes   = 0.0;
$subtotales_por_modulo  = [];
#endregion init variables

#region region Get Project Filter
// Get project filter from session (set by lproyectos.php)
if (isset($_SESSION['filtro_proyecto_costos_modulos_id'])) {
	$filtro_proyecto_id = (int)$_SESSION['filtro_proyecto_costos_modulos_id'];
	
	// Get project name for display
	try {
		$proyecto = Proyecto::get($filtro_proyecto_id, $conexion);
		$nombre_proyecto_filtro = $proyecto ? $proyecto->getDescripcion() : '';
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = "Error al obtener información del proyecto: " . $e->getMessage();
	}
	
	// Clear the session variable after use
	unset($_SESSION['filtro_proyecto_costos_modulos_id']);
}
#endregion Get Project Filter

#region region Fetch Module Cost Report Data
try {
	// Auto-load data: display all finished tasks when page loads
	$reporte_costos_modulos = Tarea::getModuleCostReportData($filtro_proyecto_id, $conexion);

	// Calculate totals and subtotals by module
	$current_module = null;
	$module_costo_usd = 0.0;
	$module_n_mensajes = 0;
	$module_costo_mensajes = 0.0;

	foreach ($reporte_costos_modulos as $registro) {
		$modulo = $registro['modulo_descripcion'];
		
		// If we're starting a new module, save the previous module's subtotal
		if ($current_module !== null && $current_module !== $modulo) {
			$subtotales_por_modulo[$current_module] = [
				'costo_usd' => $module_costo_usd,
				'n_mensajes' => $module_n_mensajes,
				'costo_total_mensajes' => $module_costo_mensajes
			];
			
			// Reset for new module
			$module_costo_usd = 0.0;
			$module_n_mensajes = 0;
			$module_costo_mensajes = 0.0;
		}
		
		$current_module = $modulo;
		
		// Add to module subtotals
		$module_costo_usd += $registro['costo_usd'] ?? 0.0;
		$module_n_mensajes += $registro['n_mensajes'] ?? 0;
		$module_costo_mensajes += $registro['costo_total_mensajes'] ?? 0.0;
		
		// Add to grand totals
		$total_costo_usd += $registro['costo_usd'] ?? 0.0;
		$total_n_mensajes += $registro['n_mensajes'] ?? 0;
		$total_costo_mensajes += $registro['costo_total_mensajes'] ?? 0.0;
	}
	
	// Don't forget the last module
	if ($current_module !== null) {
		$subtotales_por_modulo[$current_module] = [
			'costo_usd' => $module_costo_usd,
			'n_mensajes' => $module_n_mensajes,
			'costo_total_mensajes' => $module_costo_mensajes
		];
	}

} catch (Exception $e) {
	$error_display = 'show';
	$error_text = "Error al obtener el reporte de costos por módulo: " . $e->getMessage();
}
#endregion Fetch Module Cost Report Data

/**
 * Format currency in Colombian pesos
 */
function formatCOP(float $amount): string
{
	return '$' . number_format($amount, 0, ',', '.') . ' COP';
}

require_once __ROOT__ . '/views/admin/reporte_costos_modulos.view.php';

?>
